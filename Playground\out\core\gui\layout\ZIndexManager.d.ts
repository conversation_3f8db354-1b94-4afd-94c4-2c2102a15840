/**
 * Global Z-Index Management System
 * Automatically manages layering of UI elements so new windows always appear on top
 *
 * Z-Index Ranges:
 * - 500-599: Debug overlay panels (performance monitor, debug info) - background
 * - 1000+: Interactive panels (world testing, debug controls) - foreground
 * - Each interactive panel gets +50 to ensure proper stacking order
 */
declare class ZIndexManagerClass {
    private currentZIndex;
    private registeredElements;
    /**
     * Get the next available Z-Index for a new UI element
     * Each call returns a higher Z-Index, ensuring new elements appear on top
     */
    getNextZIndex(elementId?: string): number;
    /**
     * Get the current highest Z-Index without incrementing
     */
    getCurrentZIndex(): number;
    /**
     * Bring an existing element to the front
     */
    bringToFront(elementId: string): number;
    /**
     * Get the Z-Index for a specific element
     */
    getZIndex(elementId: string): number | undefined;
    /**
     * Remove an element from tracking
     */
    unregister(elementId: string): void;
    /**
     * Reset the Z-Index counter (useful for testing)
     */
    reset(): void;
    /**
     * Get all registered elements and their Z-Indices
     */
    getRegisteredElements(): Map<string, number>;
    /**
     * Get a Z-Index for debug overlay panels (background debug info)
     * These should appear above regular UI but below interactive panels
     * Uses a fixed lower range to ensure they stay behind interactive panels
     */
    getDebugOverlayZIndex(elementId?: string): number;
    /**
     * Get a high-priority Z-Index for interactive debug/system elements
     * These elements should always appear above regular UI and debug overlays
     * Uses the normal high-priority range to ensure they're always on top
     */
    getDebugZIndex(elementId?: string): number;
}
export declare const ZIndexManager: ZIndexManagerClass;
export {};
