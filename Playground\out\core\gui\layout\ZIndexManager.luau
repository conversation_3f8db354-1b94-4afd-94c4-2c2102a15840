-- Compiled with roblox-ts v3.0.0
--[[
	*
	 * Global Z-Index Management System
	 * Automatically manages layering of UI elements so new windows always appear on top
	 *
	 * Z-Index Ranges:
	 * - 500-599: Debug overlay panels (performance monitor, debug info) - background
	 * - 1000+: Interactive panels (world testing, debug controls) - foreground
	 * - Each interactive panel gets +50 to ensure proper stacking order
	 
]]
local ZIndexManagerClass
do
	ZIndexManagerClass = setmetatable({}, {
		__tostring = function()
			return "ZIndexManagerClass"
		end,
	})
	ZIndexManagerClass.__index = ZIndexManagerClass
	function ZIndexManagerClass.new(...)
		local self = setmetatable({}, ZIndexManagerClass)
		return self:constructor(...) or self
	end
	function ZIndexManagerClass:constructor()
		self.currentZIndex = 1000
		self.registeredElements = {}
	end
	function ZIndexManagerClass:getNextZIndex(elementId)
		self.currentZIndex += 10
		if elementId ~= "" and elementId then
			local _registeredElements = self.registeredElements
			local _elementId = elementId
			local _currentZIndex = self.currentZIndex
			_registeredElements[_elementId] = _currentZIndex
		end
		return self.currentZIndex
	end
	function ZIndexManagerClass:getCurrentZIndex()
		return self.currentZIndex
	end
	function ZIndexManagerClass:bringToFront(elementId)
		local newZIndex = self:getNextZIndex(elementId)
		return newZIndex
	end
	function ZIndexManagerClass:getZIndex(elementId)
		local _registeredElements = self.registeredElements
		local _elementId = elementId
		return _registeredElements[_elementId]
	end
	function ZIndexManagerClass:unregister(elementId)
		local _registeredElements = self.registeredElements
		local _elementId = elementId
		_registeredElements[_elementId] = nil
	end
	function ZIndexManagerClass:reset()
		self.currentZIndex = 1000
		table.clear(self.registeredElements)
	end
	function ZIndexManagerClass:getRegisteredElements()
		local copy = {}
		local _exp = self.registeredElements
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(value, key)
			local _key = key
			local _value = value
			copy[_key] = _value
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		return copy
	end
	function ZIndexManagerClass:getDebugOverlayZIndex(elementId)
		-- Use a fixed lower range for debug overlay panels (500-599)
		local debugOverlayBase = 500
		-- Count existing debug overlay elements to calculate offset
		local debugOverlayCount = 0
		local _exp = self.registeredElements
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(value, key)
			if value >= 500 and value < 600 then
				debugOverlayCount += 1
			end
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		local debugOverlayZIndex = debugOverlayBase + (debugOverlayCount * 5)
		if elementId ~= "" and elementId then
			local _registeredElements = self.registeredElements
			local _elementId = elementId
			_registeredElements[_elementId] = debugOverlayZIndex
		end
		print(`🔧 Debug overlay "{elementId}" assigned z-index: {debugOverlayZIndex}`)
		return debugOverlayZIndex
	end
	function ZIndexManagerClass:getDebugZIndex(elementId)
		-- Use the normal high-priority system for interactive debug elements
		self.currentZIndex += 50
		if elementId ~= "" and elementId then
			local _registeredElements = self.registeredElements
			local _elementId = elementId
			local _currentZIndex = self.currentZIndex
			_registeredElements[_elementId] = _currentZIndex
		end
		print(`🌍 Interactive debug panel "{elementId}" assigned z-index: {self.currentZIndex}`)
		return self.currentZIndex
	end
end
-- Export a singleton instance
local ZIndexManager = ZIndexManagerClass.new()
return {
	ZIndexManager = ZIndexManager,
}
