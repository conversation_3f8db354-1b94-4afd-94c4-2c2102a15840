/**
 * Global Z-Index Management System
 * Automatically manages layering of UI elements so new windows always appear on top
 *
 * Z-Index Ranges:
 * - 500-599: Debug overlay panels (performance monitor, debug info) - background
 * - 1000+: Interactive panels (world testing, debug controls) - foreground
 * - Each interactive panel gets +50 to ensure proper stacking order
 */

class ZIndexManagerClass {
    private currentZIndex = 1000; // Start at a high base value
    private registeredElements = new Map<string, number>();
    
    /**
     * Get the next available Z-Index for a new UI element
     * Each call returns a higher Z-Index, ensuring new elements appear on top
     */
    getNextZIndex(elementId?: string): number {
        this.currentZIndex += 10; // Increment by 10 to leave room for sub-elements

        if (elementId) {
            this.registeredElements.set(elementId, this.currentZIndex);
        }

        print(`📊 ZIndexManager.getNextZIndex("${elementId}") = ${this.currentZIndex}`);
        return this.currentZIndex;
    }
    
    /**
     * Get the current highest Z-Index without incrementing
     */
    getCurrentZIndex(): number {
        return this.currentZIndex;
    }
    
    /**
     * Bring an existing element to the front
     */
    bringToFront(elementId: string): number {
        const newZIndex = this.getNextZIndex(elementId);
        return newZIndex;
    }
    
    /**
     * Get the Z-Index for a specific element
     */
    getZIndex(elementId: string): number | undefined {
        return this.registeredElements.get(elementId);
    }
    
    /**
     * Remove an element from tracking
     */
    unregister(elementId: string): void {
        this.registeredElements.delete(elementId);
    }
    
    /**
     * Reset the Z-Index counter (useful for testing)
     */
    reset(): void {
        this.currentZIndex = 1000;
        this.registeredElements.clear();
    }
    
    /**
     * Get all registered elements and their Z-Indices
     */
    getRegisteredElements(): Map<string, number> {
        const copy = new Map<string, number>();
        this.registeredElements.forEach((value, key) => {
            copy.set(key, value);
        });
        return copy;
    }

    /**
     * Print a summary of all registered z-index assignments for debugging
     */
    printZIndexSummary(): void {
        print(`📊 === Z-INDEX SUMMARY (${this.registeredElements.size()} elements) ===`);

        // Sort by z-index value
        const sortedElements: Array<[string, number]> = [];
        this.registeredElements.forEach((value, key) => {
            sortedElements.push([key, value]);
        });
        sortedElements.sort((a, b) => a[1] - b[1]);

        sortedElements.forEach(([elementId, zIndex]) => {
            const priority = zIndex >= 1000 ? "[HIGH PRIORITY]" : zIndex >= 500 ? "[LOW PRIORITY]" : "[REGULAR]";
            print(`  ${elementId}: ${zIndex} ${priority}`);
        });

        print(`📊 Current highest z-index: ${this.currentZIndex}`);
        print(`📊 === END Z-INDEX SUMMARY ===`);
    }

    /**
     * Get a Z-Index for debug overlay panels (background debug info)
     * These should appear above regular UI but below interactive panels
     * Uses a fixed lower range to ensure they stay behind interactive panels
     */
    getDebugOverlayZIndex(elementId?: string): number {
        // Use a fixed lower range for debug overlay panels (500-599)
        const debugOverlayBase = 500;

        // Count existing debug overlay elements to calculate offset
        let debugOverlayCount = 0;
        this.registeredElements.forEach((value, key) => {
            if (value >= 500 && value < 600) {
                debugOverlayCount++;
            }
        });

        const debugOverlayZIndex = debugOverlayBase + (debugOverlayCount * 5);

        if (elementId) {
            this.registeredElements.set(elementId, debugOverlayZIndex);
        }

        print(`🔧 ZIndexManager.getDebugOverlayZIndex("${elementId}") = ${debugOverlayZIndex} [LOW PRIORITY]`);
        print(`📋 Debug overlay count in range 500-599: ${debugOverlayCount}`);
        return debugOverlayZIndex;
    }

    /**
     * Get a high-priority Z-Index for interactive debug/system elements
     * These elements should always appear above regular UI and debug overlays
     * Uses the normal high-priority range to ensure they're always on top
     */
    getDebugZIndex(elementId?: string): number {
        // Use the normal high-priority system for interactive debug elements
        this.currentZIndex += 50; // Large increment for interactive debug priority

        if (elementId) {
            this.registeredElements.set(elementId, this.currentZIndex);
        }

        print(`🌍 ZIndexManager.getDebugZIndex("${elementId}") = ${this.currentZIndex} [HIGH PRIORITY]`);
        print(`📋 Current registered elements: ${this.registeredElements.size()}`);
        this.registeredElements.forEach((value, key) => {
            print(`   - ${key}: ${value}`);
        });
        return this.currentZIndex;
    }
}

// Export a singleton instance
export const ZIndexManager = new ZIndexManagerClass();


